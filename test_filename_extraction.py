#!/usr/bin/env python3
"""
Test the new filename extraction and generation functionality.
"""

import sys
import os
sys.path.append('.')

from mindlink.agent import AgentOS

def test_filename_extraction():
    """Test the filename extraction from user prompts."""
    
    # Create a mock agent to test the methods
    agent = AgentOS(llm=None, system_prompt_template="", max_steps=5)
    
    # Test your original prompt
    original_prompt = """Write a file and folder structure for a Python Tic-Tac-Toe project. The root directory tic_tac_toe_project/ should contain two folders, game/ and ui/, and two files, main.py and README.md. The game/ folder must include __init__.py (0 lines) and logic.py (approx. 40 lines) for the core game logic. The ui/ folder must include __init__.py (0 lines) and console.py (approx. 25 lines) for the terminal-based user interface. The main.py file (approx. 30 lines) should contain the main game loop, and README.md (approx. 12 lines) should contain the project description. Precisely specify the responsibility and the line count for each file"""
    
    print("🧪 Testing Filename Extraction")
    print("=" * 50)
    print(f"Original prompt: {original_prompt[:100]}...")
    print()
    
    # Test filename extraction
    extracted_files = agent._extract_filenames_from_prompt(original_prompt)
    print(f"✅ Extracted filenames: {extracted_files}")
    print()
    
    # Test professional filename generation for different contexts
    test_contexts = [
        {
            'file_description_base': 'tic-tac-toe game',
            'extracted_filenames': extracted_files,
            'lines_per_file': None,
            'naming_pattern': None
        },
        {
            'file_description_base': 'web api service',
            'extracted_filenames': [],
            'lines_per_file': None,
            'naming_pattern': None
        },
        {
            'file_description_base': 'machine learning model',
            'extracted_filenames': [],
            'lines_per_file': None,
            'naming_pattern': None
        }
    ]
    
    print("🎯 Testing Filename Determination")
    print("=" * 50)
    
    for i, ctx in enumerate(test_contexts, 1):
        print(f"\nTest Context {i}: {ctx['file_description_base']}")
        print(f"Extracted files: {ctx['extracted_filenames']}")
        print("Generated filenames:")
        
        for file_num in range(1, 7):  # Test first 6 files
            filename = agent._determine_filename(ctx, file_num)
            print(f"  File {file_num}: {filename}")
    
    print("\n" + "=" * 50)
    print("✅ Filename extraction and generation test completed!")

if __name__ == "__main__":
    test_filename_extraction()
